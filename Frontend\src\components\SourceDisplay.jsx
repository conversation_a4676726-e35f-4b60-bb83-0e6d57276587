import { FileText } from 'lucide-react';

const SourceDisplay = ({ sources, contextUsed, documentMode }) => {
  // Don't show anything if not in document mode or no sources
  if (!documentMode || !contextUsed || !sources || sources.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 border-t border-white/10 pt-3">
      <div className="text-xs text-white/60 mb-2">
        Response generated using information from:
      </div>

      <div className="space-y-1">
        {sources.map((source, index) => {
          const filename = source.filename || 'Unknown Document';
          const pageNumber = source.page_number;
          const chunkIndex = source.chunk_index;

          return (
            <div key={index} className="flex items-center gap-2 text-xs text-white/80">
              <FileText className="w-3 h-3 text-blue-400 flex-shrink-0" />
              <span>
                {filename}
                {pageNumber && ` (Page ${pageNumber}`}
                {chunkIndex && chunkIndex !== 'Unknown' && `, Chunk ${chunkIndex}`}
                {pageNumber && ')'}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SourceDisplay;
